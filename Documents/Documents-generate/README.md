# 文件生成工具 (File Generator)

一个基于Python的文件生成工具，可以生成指定大小和类型的文件。

## 功能特点

- 支持多种文件类型：txt、docx、pdf、png、mp4
- 精确控制生成文件大小，误差不超过±5%（PNG文件已优化到±1%以内）
- 保持原始文件有效性，生成的文件可正常打开和使用
- 支持断点续生成，意外中断可继续生成
- 实时进度显示，估算剩余时间
- 智能文件大小限制提示，避免生成过大文件造成性能问题

## 安装要求

```bash
pip install -r requirements.txt
```

## 使用方法

1. 运行主程序：

```bash
python main.py
```

2. 在界面中选择操作：
   - 选择要生成的文件类型
   - 输入目标文件大小（KB/MB）
   - 选择基准文件（可选）
   - 点击"生成"按钮开始处理

## 文件类型大小建议

每种文件类型有推荐的大小范围，超出此范围可能会影响生成速度或质量：

| 文件类型 | 推荐大小范围 | 最大支持大小 | 备注 |
|--------|------------|------------|------|
| TXT    | 1KB - 5GB  | 10GB       | 文本文件，生成速度最快 |
| DOCX   | 10KB - 250MB | 500MB    | Word文档，结构完整可编辑 |
| PDF    | 10KB - 500MB | 1GB      | PDF文档，支持多页面生成 |
| PNG    | 10KB - 100MB | 500MB    | 图像文件，已优化大小控制精度 |
| MP4    | 1MB - 1GB    | 2GB      | 视频文件，生成速度较慢 |

## 输出文件位置

所有生成的文件默认保存在项目根目录下的`output`文件夹中。临时文件存放在`.temp`文件夹，程序异常中断后可用于恢复生成进度。

## 模块说明

### 主界面模块 (GUI)
- 基于PySide6构建的用户友好界面
- 实时进度显示和剩余时间估算
- 智能任务队列管理

### 文件处理器模块
- 基于策略模式的处理器工厂
- 支持多种文件类型的专用处理器
- 精确大小控制算法

### 进度管理模块
- 多线程任务处理
- 断点续传支持
- 异常处理和日志记录

## 开发者文档

### 添加新的文件处理器
要添加新的文件类型支持，需创建继承自`FileProcessor`的处理器类：

```python
from file_processors.base import FileProcessor

class NewTypeProcessor(FileProcessor):
    def process(self, base_file, target_size, progress_callback=None):
        # 实现处理逻辑
        pass
```

然后在工厂类中注册：

```python
factory.register("newtype", NewTypeProcessor())
```

### 异常处理
所有处理器异常继承自`FileGenerationError`基类。

## 项目结构

```
file-generator/
├── config/                  # 配置模块
│   ├── __init__.py
│   └── config.py           # 全局配置
├── file_processors/         # 文件处理器模块
│   ├── __init__.py         # 处理器注册
│   ├── base.py             # 处理器基类与工厂
│   ├── text_processor.py   # 文本处理器
│   ├── docx_processor.py   # DOCX处理器
│   ├── pdf_processor.py    # PDF处理器
│   ├── image_processor.py  # 图像处理器
│   ├── video_processor.py  # 视频处理器
│   └── utils/              # 工具模块
│       ├── __init__.py
│       ├── exceptions.py   # 异常定义
│       ├── helpers.py      # 工具函数
│       ├── logger.py       # 日志记录
│       └── progress_manager.py # 进度管理
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖列表
├── output/                 # 输出文件目录
├── .temp/                  # 临时文件目录
└── README.md               # 项目文档
```

## 技术实现

### 文本文件处理
- 基于随机文本生成和精确字节控制
- 支持UTF-8编码，确保生成文件可正常打开
- 分块处理避免内存溢出，支持大文件生成

### DOCX文件处理
- 利用python-docx库操作文档内容
- 保持文档结构完整性，可正常打开和编辑
- 智能增量添加内容直到达到目标大小

### PDF文件处理
- 使用reportlab生成PDF文档
- 支持断点续生成和精确大小控制

### 图像处理
- 操作PNG元数据和图像内容
- 添加自定义元数据块实现精确大小控制
- 三层策略确保大小精确控制：
  1. 元数据填充（最多增加30%大小）
  2. 图像复杂度增强（通过增加图像细节）
  3. 自定义数据块添加（精确控制到字节级别）
  4. 微调优化（确保误差小于1%）

### 视频处理
- 集成ffmpeg实现视频编码控制
- 支持通过比特率和CRF参数调整视频大小
- 支持向视频中附加自定义数据

## 性能优化

- 多线程处理避免UI卡顿
- 生成器模式减少内存占用
- 块处理支持大文件生成
- 断点续传机制处理意外中断

## 近期更新内容

1. **PNG精确大小控制优化** (v1.0.1)
   - 通过多策略组合优化PNG文件大小控制，将误差从之前的±5%优化到±1%以内
   - 实现了微调算法，可精确控制到字节级别

2. **文件大小限制提示增强** (v1.0.1)
   - 增加各文件类型的推荐大小范围提示
   - 超出推荐大小时显示详细警告信息
   - 状态栏实时显示当前文件类型的大小建议

## 未来改进计划

1. 批量生成支持
2. 更多文件类型支持（如Excel、音频文件）
3. 预设模板功能
4. 命令行接口
5. 自定义内容生成规则
6. 更高级的视频处理（调整分辨率、帧率等）
7. 国际化支持 