#!/usr/bin/env python3
"""文件生成工具主程序 - 用于生成指定大小和类型的文件"""

import os
import sys
import time
from typing import Optional, Tuple, List, Dict  # 类型提示，提高代码可读性

# PySide6是Qt的Python绑定，用于创建图形界面
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QComboBox, QDoubleSpinBox, QPushButton, QFileDialog,
    QProgressBar, QMessageBox, QStatusBar, QLineEdit, QGridLayout,
    QGroupBox, QSpacerItem, QSizePolicy
)
from PySide6.QtCore import Qt, QTimer, Slot, Signal, QThread  # Qt核心功能
from PySide6.QtGui import QIcon, QFont, QDoubleValidator  # Qt图形界面元素

# 导入自定义模块
from file_processors import get_processor, get_supported_types  # 文件处理器工厂函数
from file_processors.utils.helpers import convert_size_to_bytes, format_bytes, ensure_dir_exists  # 辅助函数
from file_processors.utils.exceptions import FileGenerationError, InterruptedGenerationError  # 自定义异常
from file_processors.utils.progress_manager import FileGenerationWorker  # 进度管理和后台线程
from file_processors.utils.logger import log_info, log_error, log_debug, log_warning  # 日志记录工具
from config.config import FileType, SizeUnit, get_config  # 配置管理

class MainWindow(QMainWindow):
    """文件生成器主窗口 - 整个应用的UI和功能入口"""
    
    def __init__(self):
        super().__init__()
        
        # 文件类型推荐大小限制
        self.file_type_limits = {
            "txt": "10GB",
            "docx": "500MB", 
            "pdf": "1GB",
            "png": "500MB",
            "mp4": "2GB"
        }
        
        # 文件类型警告阈值 (超过此值显示警告)
        self.file_type_warnings = {
            "txt": 5 * 1024,  # 5GB (MB单位)
            "docx": 250,      # 250MB
            "pdf": 500,       # 500MB
            "png": 100,       # 100MB (PNG文件过大可能导致生成缓慢)
            "mp4": 1024       # 1GB
        }
        
        # 初始化UI界面
        self.init_ui()
        
        # 生成任务相关变量初始化
        self.worker: Optional[FileGenerationWorker] = None  # 后台工作线程
        self.worker_thread: Optional[QThread] = None  # Qt线程实例
        self.start_time = 0  # 任务开始时间，用于计算剩余时间
        
        # 确保输出和临时目录存在
        ensure_dir_exists(get_config("OUTPUT_DIR", "output"))  # 输出目录
        ensure_dir_exists(get_config("TEMP_DIR", ".temp"))  # 临时文件目录
        
    def init_ui(self):
        """初始化用户界面 - 设置窗口布局和各UI组件"""
        # 设置窗口标题和大小
        self.setWindowTitle(f"{get_config('APP_NAME')} v{get_config('APP_VERSION')}")
        self.setMinimumSize(600, 400)
        
        # 创建中央部件，所有UI元素将添加到这里
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局 - 垂直排列各元素组
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)  # 设置边距
        main_layout.setSpacing(15)  # 设置元素间距
        
        # ===== 参数设置组 =====
        param_group = QGroupBox("文件参数")  # 创建分组框
        param_layout = QGridLayout()  # 网格布局，便于对齐表单元素
        param_group.setLayout(param_layout)
        
        # 文件类型选择下拉框
        param_layout.addWidget(QLabel("文件类型:"), 0, 0)
        self.file_type_combo = QComboBox()
        for file_type in get_supported_types():  # 添加所有支持的文件类型
            self.file_type_combo.addItem(file_type.upper(), file_type)  # 显示为大写，值为小写
        param_layout.addWidget(self.file_type_combo, 0, 1, 1, 2)
        
        # 目标大小数值输入框
        param_layout.addWidget(QLabel("目标大小:"), 1, 0)
        self.size_spin = QDoubleSpinBox()
        self.size_spin.setRange(0.1, 10000)  # 设置范围
        self.size_spin.setValue(1.0)  # 默认值
        self.size_spin.setDecimals(2)  # 小数位数
        param_layout.addWidget(self.size_spin, 1, 1)
        
        # 单位选择下拉框(KB/MB)
        self.unit_combo = QComboBox()
        self.unit_combo.addItem("KB", SizeUnit.KB.value)
        self.unit_combo.addItem("MB", SizeUnit.MB.value)
        self.unit_combo.setCurrentIndex(1)  # 默认选择MB
        param_layout.addWidget(self.unit_combo, 1, 2)
        
        # 添加推荐大小提示标签
        param_layout.addWidget(QLabel("推荐最大值:"), 2, 0)
        self.size_limit_label = QLabel("500MB")  # 默认显示
        self.size_limit_label.setStyleSheet("color: #666;")  # 灰色文本
        param_layout.addWidget(self.size_limit_label, 2, 1, 1, 2)
        
        # 基准文件选择
        param_layout.addWidget(QLabel("基准文件:"), 3, 0)
        self.base_file_edit = QLineEdit()  # 显示所选文件路径
        self.base_file_edit.setReadOnly(True)  # 只读，防止手动输入
        param_layout.addWidget(self.base_file_edit, 3, 1)
        
        # 浏览按钮 - 打开文件选择对话框
        self.browse_btn = QPushButton("浏览...")
        self.browse_btn.clicked.connect(self.browse_file)  # 连接到文件浏览方法
        param_layout.addWidget(self.browse_btn, 3, 2)
        
        # 添加参数组到主布局
        main_layout.addWidget(param_group)
        
        # ===== 操作按钮区域 =====
        action_layout = QHBoxLayout()  # 水平布局
        
        # 生成文件按钮
        self.generate_btn = QPushButton("生成文件")
        self.generate_btn.setMinimumHeight(40)  # 设置最小高度
        self.generate_btn.clicked.connect(self.start_generation)  # 连接到生成方法
        action_layout.addWidget(self.generate_btn)
        
        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setMinimumHeight(40)
        self.cancel_btn.setEnabled(False)  # 初始禁用
        self.cancel_btn.clicked.connect(self.cancel_generation)  # 连接到取消方法
        action_layout.addWidget(self.cancel_btn)
        
        # 添加操作区到主布局
        main_layout.addLayout(action_layout)
        
        # ===== 进度显示区域 =====
        progress_group = QGroupBox("生成进度")
        progress_layout = QVBoxLayout()
        progress_group.setLayout(progress_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)  # 设置范围0-100
        self.progress_bar.setValue(0)  # 初始值
        self.progress_bar.setTextVisible(True)  # 显示进度文本
        progress_layout.addWidget(self.progress_bar)
        
        # 进度信息区域 - 水平布局
        info_layout = QHBoxLayout()
        
        # 状态标签
        info_layout.addWidget(QLabel("状态:"))
        self.status_label = QLabel("就绪")  # 显示当前状态
        info_layout.addWidget(self.status_label, 1)  # 1表示拉伸因子
        
        # 剩余时间标签
        info_layout.addWidget(QLabel("剩余时间:"))
        self.eta_label = QLabel("--:--")  # 预计完成时间
        info_layout.addWidget(self.eta_label)
        
        # 添加信息布局到进度组
        progress_layout.addLayout(info_layout)
        
        # 添加进度组到主布局
        main_layout.addWidget(progress_group)
        
        # 添加弹性空间 - 确保元素靠上排列，底部留白
        main_layout.addItem(QSpacerItem(20, 20, QSizePolicy.Minimum, QSizePolicy.Expanding))
        
        # 状态栏 - 用于显示额外信息
        self.statusBar().showMessage("就绪")
        
        # 信号连接 - 当文件类型变化时更新过滤器和限制提示
        self.file_type_combo.currentIndexChanged.connect(self.update_file_filter)
        self.file_type_combo.currentIndexChanged.connect(self.update_size_limit_hint)
        
        # 初始化大小限制提示
        self.update_size_limit_hint()
        
    def browse_file(self):
        """浏览基准文件 - 打开文件选择对话框"""
        # 获取当前选择的文件类型
        file_type = self.file_type_combo.currentData()
        # 根据类型设置文件选择过滤器
        filter_text = f"{file_type.upper()} 文件 (*.{file_type})"
        
        # 打开文件选择对话框
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择基准文件", "", filter_text
        )
        
        # 如果用户选择了文件，则更新文本框
        if file_path:
            self.base_file_edit.setText(file_path)
    
    def update_file_filter(self):
        """更新文件过滤器 - 当文件类型变化时调用"""
        # 清除当前基准文件，因为类型变了，原文件可能不适用
        self.base_file_edit.clear()
    
    def update_size_limit_hint(self):
        """更新推荐大小限制提示 - 根据所选文件类型"""
        file_type = self.file_type_combo.currentData()
        limit = self.file_type_limits.get(file_type, "未知")
        self.size_limit_label.setText(f"{limit} (推荐)")
        
        # 为不同文件类型设置不同的样式和提示信息
        if file_type == "png":
            # PNG文件特别提示，因为之前有误差问题已解决
            self.size_limit_label.setStyleSheet("color: #E67E22;")  # 橙色提示
            self.statusBar().showMessage("PNG文件建议大小: 10KB-100MB，已优化精确大小控制算法")
        elif file_type == "mp4":
            # 视频文件生成耗时，给予特别提示
            self.size_limit_label.setStyleSheet("color: #3498DB;")  # 蓝色提示
            self.statusBar().showMessage("视频生成耗时较长，建议大小: 1MB-1GB")
        elif file_type == "docx":
            self.size_limit_label.setStyleSheet("color: #27AE60;")  # 绿色提示
            self.statusBar().showMessage("Word文档建议大小: 100KB-250MB")
        elif file_type == "pdf":
            self.size_limit_label.setStyleSheet("color: #8E44AD;")  # 紫色提示
            self.statusBar().showMessage("PDF文档建议大小: 100KB-500MB")
        else:
            # 其他文件类型使用普通样式
            self.size_limit_label.setStyleSheet("color: #666;")  # 灰色文本
            self.statusBar().showMessage("就绪")
    
    @Slot()  # Qt槽函数装饰器
    def start_generation(self):
        """开始生成文件 - 验证参数并启动生成线程"""
        # 获取用户设置的参数
        file_type = self.file_type_combo.currentData()  # 文件类型
        size_value = self.size_spin.value()  # 大小数值
        size_unit = SizeUnit(self.unit_combo.currentData())  # 大小单位
        base_file = self.base_file_edit.text()  # 基准文件路径
        
        # ===== 参数验证 =====
        if not base_file:
            QMessageBox.warning(self, "警告", "请选择基准文件")
            return
            
        if not os.path.exists(base_file):
            QMessageBox.warning(self, "警告", "基准文件不存在")
            return
            
        if size_value <= 0:
            QMessageBox.warning(self, "警告", "目标大小必须大于0")
            return
            
        # 检查是否超过推荐上限
        recommended_limit = self.file_type_limits.get(file_type, "10000MB")
        limit_number = float(''.join(filter(lambda c: c.isdigit() or c == '.', recommended_limit)))
        limit_unit = 'MB' if 'MB' in recommended_limit else 'KB' if 'KB' in recommended_limit else 'GB'
        current_unit = "MB" if size_unit == SizeUnit.MB else "KB"
        
        # 获取警告阈值
        warning_threshold = self.file_type_warnings.get(file_type, float('inf'))
        current_size_mb = size_value if current_unit == "MB" else size_value / 1024
        
        # 超出警告阈值但未超出限制时显示警告
        if current_size_mb > warning_threshold:
            reply = QMessageBox.question(
                self, "超出推荐大小", 
                f"您正在尝试生成 {size_value} {current_unit} 的 {file_type.upper()} 文件，\n"
                f"超过推荐大小 {warning_threshold} MB。\n\n"
                f"文件类型 {file_type.upper()} 建议的大小范围:\n"
                f"- 最小: {10 if file_type == 'png' else 1} KB\n"
                f"- 最大: {warning_threshold} MB (推荐)\n"
                f"- 极限: {recommended_limit} (可能需要较长时间)\n\n"
                f"是否继续？",
                QMessageBox.Yes | QMessageBox.No, 
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                return
        
        # 超出硬限制时的警告（与之前的代码合并）
        elif limit_unit == "GB" and current_unit == "MB" and size_value > limit_number * 1024:
            reply = QMessageBox.question(
                self, "超出最大大小", 
                f"您正在尝试生成 {size_value} {current_unit} 的 {file_type.upper()} 文件，\n超过最大支持大小 {recommended_limit}。\n\n此操作可能需要极长时间或导致程序崩溃，是否继续？",
                QMessageBox.Yes | QMessageBox.No, 
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                return
        elif limit_unit == "MB" and current_unit == "MB" and size_value > limit_number:
            reply = QMessageBox.question(
                self, "超出最大大小", 
                f"您正在尝试生成 {size_value} {current_unit} 的 {file_type.upper()} 文件，\n超过最大支持大小 {recommended_limit}。\n\n此操作可能需要极长时间或导致程序崩溃，是否继续？",
                QMessageBox.Yes | QMessageBox.No, 
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                return
        
        # 获取对应类型的处理器
        try:
            processor = get_processor(file_type)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法获取处理器: {str(e)}")
            return
            
        # 转换目标大小为字节
        target_size = convert_size_to_bytes(size_value, size_unit)
        
        # 创建工作线程 - 在后台处理文件生成任务
        self.worker = FileGenerationWorker(processor, base_file, target_size)
        
        # 连接信号 - 用于更新UI
        self.worker.signals.progress.connect(self.update_progress)  # 进度更新
        self.worker.signals.status.connect(self.update_status)  # 状态更新
        self.worker.signals.completed.connect(self.handle_completed)  # 完成信号
        self.worker.signals.error.connect(self.handle_error)  # 错误信号
        
        # 更新UI状态 - 禁用控件防止运行中修改
        self.generate_btn.setEnabled(False)  # 禁用生成按钮
        self.cancel_btn.setEnabled(True)  # 启用取消按钮
        self.file_type_combo.setEnabled(False)  # 禁用类型选择
        self.size_spin.setEnabled(False)  # 禁用大小输入
        self.unit_combo.setEnabled(False)  # 禁用单位选择
        self.browse_btn.setEnabled(False)  # 禁用文件浏览
        
        # 记录开始时间 - 用于计算剩余时间
        self.start_time = time.time()
        
        # 启动线程 - 开始处理
        self.worker.start()
        
        # 记录日志
        log_info(f"开始生成 {file_type} 文件，目标大小: {format_bytes(target_size)}")
        
    @Slot()
    def cancel_generation(self):
        """取消生成操作 - 停止当前运行的生成任务"""
        if self.worker and self.worker.isRunning():
            # 询问用户是否确认取消
            reply = QMessageBox.question(
                self, "确认取消", 
                "确定要取消当前操作吗？\n已生成的临时文件将被保留，可以下次继续。",
                QMessageBox.Yes | QMessageBox.No, 
                QMessageBox.No  # 默认选择"否"，防止误操作
            )
            
            if reply == QMessageBox.Yes:
                self.worker.cancel()  # 设置取消标志
                self.worker.wait()  # 等待线程安全结束
                self.reset_ui()  # 重置UI状态
                self.status_label.setText("已取消")
                self.statusBar().showMessage("操作已取消")
                log_info("用户取消了生成操作")
    
    @Slot(float)
    def update_progress(self, progress):
        """更新进度条和剩余时间显示 - 由工作线程的信号触发"""
        # 计算百分比并更新进度条
        percent = int(progress * 100)
        self.progress_bar.setValue(percent)
        
        # 计算剩余时间
        elapsed = time.time() - self.start_time  # 已经过时间
        if progress > 0:
            total_time = elapsed / progress  # 预计总时间
            remaining = total_time - elapsed  # 剩余时间
            
            # 格式化为时:分:秒
            minutes, seconds = divmod(int(remaining), 60)
            hours, minutes = divmod(minutes, 60)
            
            # 根据时间长短选择格式
            if hours > 0:
                eta_text = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
            else:
                eta_text = f"{minutes:02d}:{seconds:02d}"
                
            # 更新剩余时间标签
            self.eta_label.setText(eta_text)
    
    @Slot(str)
    def update_status(self, status):
        """更新状态文本 - 显示当前处理状态"""
        self.status_label.setText(status)  # 更新状态标签
        self.statusBar().showMessage(status)  # 更新状态栏
    
    @Slot(str)
    def handle_completed(self, output_file):
        """处理任务完成信号 - 显示完成信息"""
        # 计算总用时
        elapsed = time.time() - self.start_time
        # 获取生成文件大小
        file_size = os.path.getsize(output_file) if os.path.exists(output_file) else 0
        
        # 重置UI状态
        self.reset_ui()
        
        # 更新状态显示
        self.status_label.setText("完成")
        self.statusBar().showMessage(f"生成完成，用时: {elapsed:.1f}秒")
        self.progress_bar.setValue(100)  # 进度条设为100%
        
        # 显示成功消息对话框
        QMessageBox.information(
            self, 
            "成功", 
            f"文件生成成功!\n\n"
            f"输出文件: {output_file}\n"
            f"文件大小: {format_bytes(file_size)}\n"
            f"用时: {elapsed:.1f}秒"
        )
        
        # 记录日志
        log_info(f"文件生成成功: {output_file}, 大小: {format_bytes(file_size)}, 用时: {elapsed:.1f}秒")
    
    @Slot(str)
    def handle_error(self, error_msg):
        """处理错误信号 - 显示错误信息"""
        # 重置UI状态
        self.reset_ui()
        
        # 更新状态显示
        self.status_label.setText("错误")
        self.statusBar().showMessage("生成失败")
        
        # 检查是否是中断异常 - 如果是中断，询问是否下次继续
        if "InterruptedGenerationError" in error_msg:
            reply = QMessageBox.question(
                self,
                "生成中断",
                f"{error_msg}\n\n是否在下次生成时继续？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes  # 默认选择"是"
            )
            
            if reply == QMessageBox.Yes:
                # 下次继续的代码（当前仅设置标志，实际实现在处理器中）
                pass
        else:
            # 一般错误显示
            QMessageBox.critical(self, "错误", f"生成失败: {error_msg}")
            
        # 记录错误日志
        log_error(f"生成失败: {error_msg}")
    
    def reset_ui(self):
        """重置UI状态 - 恢复控件为初始状态"""
        self.generate_btn.setEnabled(True)  # 启用生成按钮
        self.cancel_btn.setEnabled(False)  # 禁用取消按钮
        self.file_type_combo.setEnabled(True)  # 启用类型选择
        self.size_spin.setEnabled(True)  # 启用大小输入
        self.unit_combo.setEnabled(True)  # 启用单位选择
        self.browse_btn.setEnabled(True)  # 启用文件浏览
        self.eta_label.setText("--:--")  # 重置剩余时间显示
        
def main():
    """主函数 - 程序入口点"""
    # 创建Qt应用实例
    app = QApplication(sys.argv)
    
    # 设置应用样式 - Fusion风格，现代外观
    app.setStyle("Fusion")
    
    # 创建并显示主窗口
    window = MainWindow()
    window.show()
    
    # 进入Qt事件循环
    sys.exit(app.exec())

# 程序入口判断
if __name__ == "__main__":
    main()  # 如果作为主程序运行，则执行main函数 