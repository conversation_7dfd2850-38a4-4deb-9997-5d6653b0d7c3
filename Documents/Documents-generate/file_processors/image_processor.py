import os
import tempfile
import random
import io
import struct
import zlib
from typing import Optional, Callable, Tuple, List, Dict, Any
from PIL import Image, ImageDraw, ImageFont, PngImagePlugin

from file_processors.base import FileProcessor
from file_processors.utils.helpers import (
    get_file_size, get_output_filepath, get_temp_filepath,
    ensure_dir_exists
)
from file_processors.utils.logger import log_info, log_debug, log_error, log_warning
from file_processors.utils.exceptions import InterruptedGenerationError, FormatMismatchError
from config.config import get_config, SizeUnit

class PngProcessor(FileProcessor):
    """PNG图像处理器 - 用于生成指定大小的PNG图像文件"""
    
    file_type = "png"
    
    def __init__(self):
        super().__init__()
        # 初始化自定义数据块ID - 使用合法但非标准的PNG块
        self.custom_chunk_id = b'fdAT'  # 自定义数据块标识符
        # 设置最大重试次数
        self.max_adjustment_attempts = 5  # 允许重试的最大次数以达到精确大小
        
    def process(
        self, 
        base_file: str, 
        target_size: int, 
        progress_callback: Optional[Callable[[float, str], None]] = None
    ) -> str:
        """
        处理PNG图像以达到目标大小
        
        Args:
            base_file: 基础PNG文件路径，用作生成内容的基础
            target_size: 目标文件大小（字节），最终生成文件的期望大小
            progress_callback: 进度回调函数，用于更新UI显示进度
            
        Returns:
            生成的文件路径
            
        Raises:
            FormatMismatchError: 如果输入文件无法解析为PNG
            InterruptedGenerationError: 如果生成过程被中断
        """
        # 校验输入文件有效性
        self.validate_base_file(base_file)
        
        # 确定输出文件路径
        output_file = get_output_filepath(
            base_file, 
            target_size / 1024 if target_size < 1024*1024 else target_size / (1024*1024), 
            SizeUnit.KB if target_size < 1024*1024 else SizeUnit.MB
        )
        
        # 处理临时文件 - 用于支持断点续传
        temp_file = get_temp_filepath(output_file)
        
        try:
            # 加载基础图像
            try:
                base_img = Image.open(base_file)
                if base_img.format != 'PNG':
                    log_debug(f"图像格式转换：{base_img.format} -> PNG")
            except Exception as e:
                raise FormatMismatchError(f"无法读取PNG文件: {str(e)}")
                
            if progress_callback:
                progress_callback(0.1, "分析基础图像")
                
            # 基础图像属性
            base_size = get_file_size(base_file)
            
            # 图像尺寸计算
            width, height = base_img.size
            
            # 如果基础图像大于目标大小，采用缩小策略
            if base_size > target_size:
                if progress_callback:
                    progress_callback(0.2, "缩小图像以达到目标大小")
                
                # 缩小图像尺寸
                ratio = 0.9  # 初始缩放比例
                new_img = base_img
                
                while get_file_size(temp_file) > target_size and ratio > 0.1:
                    new_width = int(width * ratio)
                    new_height = int(height * ratio)
                    new_img = base_img.resize((new_width, new_height), Image.LANCZOS)
                    
                    # 保存并检查大小
                    self._save_optimized_png(new_img, temp_file)
                    ratio -= 0.1
                    
                    if progress_callback:
                        progress_callback(0.5, f"尝试缩放比例: {ratio:.1f}")
                
            else:  # 基础图像小于目标大小，采用多种填充策略
                if progress_callback:
                    progress_callback(0.2, "应用多策略填充")
                
                # 首先复制基础图像
                self._save_optimized_png(base_img, temp_file)
                
                # 应用多层填充策略
                self._apply_size_expansion_strategies(temp_file, target_size, progress_callback)
                
                # 精确调整文件大小 - 新增步骤
                self._fine_tune_file_size(temp_file, target_size, progress_callback)
                
            # 验证结果并移动到最终位置
            ensure_dir_exists(os.path.dirname(output_file))
            if os.path.exists(output_file):
                os.remove(output_file)
            os.rename(temp_file, output_file)
            
            # 验证生成的文件
            self.verify_result(output_file, target_size)
            
            return output_file
            
        except Exception as e:
            # 如果失败但有进度，则包装为中断异常以便后续恢复
            if os.path.exists(temp_file) and get_file_size(temp_file) > 0:
                if not isinstance(e, InterruptedGenerationError):
                    bytes_generated = get_file_size(temp_file)
                    raise InterruptedGenerationError(
                        f"文件生成被中断: {str(e)}",
                        bytes_generated, 
                        target_size,
                        temp_file
                    )
            raise
    
    def _save_optimized_png(self, img: Image.Image, output_path: str) -> None:
        """保存优化的PNG图像
        
        Args:
            img: PIL图像对象
            output_path: 输出文件路径
        """
        # 创建输出目录
        ensure_dir_exists(os.path.dirname(output_path))
        
        # 保存图像 - optimize=True可以减小文件大小
        img.save(output_path, format='PNG', optimize=True)
    
    def _apply_size_expansion_strategies(
        self,
        png_path: str,
        target_size: int,
        progress_callback: Optional[Callable[[float, str], None]] = None
    ) -> None:
        """应用多种扩展策略以达到目标文件大小
        
        Args:
            png_path: PNG文件路径
            target_size: 目标大小(字节)
            progress_callback: 进度回调函数
        """
        # 获取初始大小
        current_size = get_file_size(png_path)
        if current_size >= target_size:
            return
            
        # 计算需要添加的数据量
        remaining = target_size - current_size
        log_debug(f"需要添加 {remaining} 字节的数据")

        # 策略1: 添加元数据 (最多可增加大约20-30%)
        if progress_callback:
            progress_callback(0.3, "策略1: 添加元数据...")
            
        self._extend_with_metadata(png_path, min(target_size, current_size * 1.3))
        current_size = get_file_size(png_path)
        
        # 如果元数据添加后仍然不足，继续下一策略
        if current_size < target_size:
            remaining = target_size - current_size
            log_debug(f"策略1后仍需 {remaining} 字节")
            
            # 策略2: 增加图像复杂度
            if progress_callback:
                progress_callback(0.5, "策略2: 增加图像复杂度...")
                
            self._enhance_image_complexity(png_path, min(target_size, current_size * 1.5))
            current_size = get_file_size(png_path)
        
        # 如果仍然不足，使用自定义数据块直接添加精确大小的数据
        if current_size < target_size:
            remaining = target_size - current_size
            log_debug(f"策略2后仍需 {remaining} 字节，使用自定义数据块")
            
            if progress_callback:
                progress_callback(0.7, "策略3: 添加自定义数据块...")
                
            # 注意：在这里我们不尝试精确达到目标大小，而是添加略小于所需的数据量
            # 精确调整会在_fine_tune_file_size中完成
            self._add_custom_chunks(png_path, target_size - 100)  # 预留100字节用于精确调整
            current_size = get_file_size(png_path)
            
        if progress_callback:
            progress_callback(0.8, "完成初步大小调整")
    
    def _extend_with_metadata(self, png_path: str, target_size: int) -> None:
        """通过添加元数据扩展PNG文件大小
        
        Args:
            png_path: PNG文件路径
            target_size: 目标大小(字节)
        """
        current_size = get_file_size(png_path)
        if current_size >= target_size:
            return
            
        # 添加自定义元数据
        img = Image.open(png_path)
        metadata = PngImagePlugin.PngInfo()
        
        # 添加一些基本元数据
        metadata.add_text("Software", "FileGenerator")
        metadata.add_text("Description", "Generated PNG file with precise size control")
        metadata.add_text("Creator", "FileGenerator PNG Processor")
        metadata.add_text("GenerationTime", "")  # 空字段会占用最少字节
        
        # 计算需要添加的数据量
        remaining = target_size - current_size
        
        # 添加大量自定义元数据直到达到目标大小
        # 元数据块大小增加到4KB，提高填充效率
        chunk_size = 4096  # 元数据块大小
        chunks_added = 0
        max_chunks = 5000  # 增加最大块数量
        
        while current_size < target_size and chunks_added < max_chunks:
            # 生成随机键和值
            key = f"CustomData{chunks_added:06d}"
            value_size = min(remaining, chunk_size)
            value = ''.join(random.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') 
                           for _ in range(value_size))
            
            # 添加元数据
            metadata.add_text(key, value)
            chunks_added += 1
            
            # 临时保存以检查大小，每20块检查一次，提高效率
            if chunks_added % 20 == 0:
                # 保存图像
                img.save(png_path, format='PNG', pnginfo=metadata)
                current_size = get_file_size(png_path)
                remaining = target_size - current_size
                log_debug(f"已添加 {chunks_added} 个元数据块，当前大小: {current_size} 字节")
        
        # 最终保存
        img.save(png_path, format='PNG', pnginfo=metadata)
        log_debug(f"元数据策略完成，添加了 {chunks_added} 个元数据块")
    
    def _enhance_image_complexity(self, png_path: str, target_size: int) -> None:
        """通过增加图像复杂度来增大PNG文件大小
        
        Args:
            png_path: PNG文件路径
            target_size: 目标大小(字节)
        """
        current_size = get_file_size(png_path)
        if current_size >= target_size:
            return
        
        # 读取图像
        img = Image.open(png_path)
        width, height = img.size
        
        # 策略2.1: 提高图像分辨率 - 扩大2倍
        scale_factor = 2.0
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        new_img = img.resize((new_width, new_height), Image.LANCZOS)
        
        # 策略2.2: 在图像上添加噪点和复杂图形
        draw = ImageDraw.Draw(new_img)
        
        # 绘制大量随机线条、圆形和矩形
        for _ in range(500):  # 增加到500个元素
            shape_type = random.choice(['line', 'circle', 'rectangle'])
            color = (random.randint(0, 255), random.randint(0, 255), random.randint(0, 255), random.randint(10, 200))
            
            if shape_type == 'line':
                x1 = random.randint(0, new_width - 1)
                y1 = random.randint(0, new_height - 1)
                x2 = random.randint(0, new_width - 1)
                y2 = random.randint(0, new_height - 1)
                draw.line([(x1, y1), (x2, y2)], fill=color, width=random.randint(1, 5))
            elif shape_type == 'circle':
                x = random.randint(0, new_width - 1)
                y = random.randint(0, new_height - 1)
                radius = random.randint(5, 50)
                draw.ellipse((x-radius, y-radius, x+radius, y+radius), outline=color, width=random.randint(1, 3))
            else:  # rectangle
                x1 = random.randint(0, new_width - 50)
                y1 = random.randint(0, new_height - 50)
                x2 = x1 + random.randint(10, 50)
                y2 = y1 + random.randint(10, 50)
                draw.rectangle([x1, y1, x2, y2], outline=color, width=random.randint(1, 3))
        
        # 添加一些随机渐变效果
        for _ in range(10):
            x1 = random.randint(0, new_width - 100)
            y1 = random.randint(0, new_height - 100)
            width = random.randint(50, 200)
            height = random.randint(50, 200)
            
            # 创建渐变区域
            for i in range(width):
                alpha = int(255 * (i / width))
                color = (random.randint(0, 255), random.randint(0, 255), random.randint(0, 255), alpha)
                draw.line([(x1+i, y1), (x1+i, y1+height)], fill=color, width=1)
        
        # 保存增强后的图像
        new_img.save(png_path, format='PNG')
        log_debug(f"图像复杂度增强完成，当前大小: {get_file_size(png_path)} 字节")
    
    def _add_custom_chunks(self, png_path: str, target_size: int) -> None:
        """添加自定义PNG数据块以精确控制文件大小
        
        利用PNG格式允许添加自定义数据块的特性，直接操作二进制文件
        
        Args:
            png_path: PNG文件路径
            target_size: 目标大小(字节)
        """
        current_size = get_file_size(png_path)
        if current_size >= target_size:
            return
            
        # 计算需要添加的字节数
        remaining = target_size - current_size
        
        # 读取原始PNG文件
        with open(png_path, 'rb') as f:
            png_data = f.read()
            
        # 确保是有效的PNG (检查文件头)
        if not png_data.startswith(b'\x89PNG\r\n\x1a\n'):
            log_warning("无效的PNG文件头，无法添加自定义数据块")
            return
            
        # 查找IEND块位置(PNG文件的结束标记)
        iend_pos = png_data.find(b'IEND')
        if iend_pos == -1:
            log_warning("找不到PNG文件的IEND标记")
            return
            
        # 随机数据，使用zlib压缩可减小添加的有效数据量
        # 但由于PNG数据块格式要求，最终文件增加量会大于请求的数据量
        # 计算需要添加的数据量的调整值
        # 每个PNG块增加: 4字节(长度) + 4字节(类型) + 数据 + 4字节(CRC)
        # 因此实际添加的数据应少于requested_size
        adjusted_data_size = remaining - 12  # 12是PNG块的额外开销(长度+类型+CRC)
        
        if adjusted_data_size <= 0:
            # 如果需要添加的数据太少，无法满足PNG块要求，则使用最小的数据块
            adjusted_data_size = 1
            
        # 生成随机数据
        random_data = bytearray(random.getrandbits(8) for _ in range(adjusted_data_size))
        
        # 创建自定义数据块
        chunk_type = self.custom_chunk_id  # 使用自定义的数据块类型标识符
        chunk_data = bytes(random_data)
        chunk_length = struct.pack('>I', len(chunk_data))  # 大端字节序的4字节无符号整数
        
        # 计算CRC32校验和
        crc = zlib.crc32(chunk_type + chunk_data) & 0xffffffff
        chunk_crc = struct.pack('>I', crc)
        
        # 构建完整的数据块
        custom_chunk = chunk_length + chunk_type + chunk_data + chunk_crc
        
        # 在IEND块前插入自定义数据块
        new_png_data = png_data[:iend_pos-4] + custom_chunk + png_data[iend_pos-4:]
        
        # 写回文件
        with open(png_path, 'wb') as f:
            f.write(new_png_data)
        
        # 验证大小
        final_size = get_file_size(png_path)
        log_debug(f"添加自定义数据块完成，目标: {target_size}字节，实际: {final_size}字节，差异: {final_size-target_size}字节")
        
    def _fine_tune_file_size(
        self, 
        png_path: str, 
        target_size: int,
        progress_callback: Optional[Callable[[float, str], None]] = None
    ) -> None:
        """精确调整文件大小 - 通过小型自定义数据块微调至精确大小
        
        Args:
            png_path: PNG文件路径
            target_size: 目标大小(字节)
            progress_callback: 进度回调函数
        """
        if progress_callback:
            progress_callback(0.85, "精确调整文件大小...")
            
        current_size = get_file_size(png_path)
        size_diff = target_size - current_size
        
        # 如果大小差异在±1%范围内，进行精确调整
        # 如果文件过大，我们无法轻易缩小它，但可以通过添加小型数据块增加大小
        if size_diff > 0 and size_diff < 10000:  # 最多补充10KB
            attempt = 0
            
            while current_size < target_size and attempt < self.max_adjustment_attempts:
                # 读取原始PNG文件
                with open(png_path, 'rb') as f:
                    png_data = f.read()
                
                # 查找IEND块位置
                iend_pos = png_data.find(b'IEND')
                if iend_pos == -1:
                    break
                    
                # 计算需要补充的确切字节数
                # 考虑到添加块的开销(12字节)
                bytes_to_add = target_size - current_size
                
                # 根据需要添加的字节数决定策略
                if bytes_to_add <= 12:  # 如果需要添加的字节数小于块头的大小
                    # 使用最小数据块，微调CRC或数据部分以达到精确大小
                    chunk_data = b'\x00'  # 1字节数据
                    padding_bytes = bytes_to_add - 9  # 9是最小块的必要开销
                    if padding_bytes > 0:
                        chunk_data = b'\x00' * padding_bytes
                else:
                    # 正常添加一个精确大小的数据块
                    chunk_data_size = bytes_to_add - 12  # 减去块头和CRC的12字节
                    chunk_data = bytes(random.getrandbits(8) for _ in range(chunk_data_size))
                
                # 创建精确大小的数据块
                chunk_type = b'fTnE'  # 使用不同的块标识符，区分微调块
                chunk_length = struct.pack('>I', len(chunk_data))
                crc = zlib.crc32(chunk_type + chunk_data) & 0xffffffff
                chunk_crc = struct.pack('>I', crc)
                
                # 构建完整的数据块
                fine_tune_chunk = chunk_length + chunk_type + chunk_data + chunk_crc
                
                # 在IEND块前插入微调数据块
                new_png_data = png_data[:iend_pos-4] + fine_tune_chunk + png_data[iend_pos-4:]
                
                # 写回文件
                with open(png_path, 'wb') as f:
                    f.write(new_png_data)
                
                # 检查调整后的大小
                current_size = get_file_size(png_path)
                attempt += 1
                
                log_debug(f"微调尝试 {attempt}: 目标 {target_size}, 当前 {current_size}, 差异 {target_size-current_size}")
            
            if progress_callback:
                progress_callback(0.9, f"完成精确调整，误差: {abs(target_size-current_size)} 字节")
            
            # 计算误差百分比
            error_percent = abs(target_size - current_size) / target_size * 100
            log_debug(f"最终误差: {error_percent:.2f}%")
            
        else:
            log_debug(f"不进行精确调整，当前大小差异过大: {size_diff} 字节") 