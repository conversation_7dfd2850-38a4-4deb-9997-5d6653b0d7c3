import os
from abc import ABC, abstractmethod  # 抽象基类模块，用于定义接口
from typing import Optional, Callable, Dict, Any, ClassVar  # 类型提示

from file_processors.utils.exceptions import FileGenerationError, BaseFileUnavailableError  # 异常类
from file_processors.utils.logger import log_info, log_error, log_debug, log_warning  # 日志工具
from file_processors.utils.helpers import get_file_size, verify_size_error  # 辅助函数

class FileProcessor(ABC):
    """文件处理器基类 - 所有具体文件类型处理器的抽象基类，定义了处理接口"""
    
    file_type: ClassVar[str] = "base"  # 文件类型标识，子类必须重写此属性
    
    def __init__(self):
        """初始化处理器 - 子类可以扩展此方法添加额外的初始化逻辑"""
        pass
    
    @abstractmethod
    def process(
        self, 
        base_file: str,  # 基准文件路径
        target_size: int,  # 目标大小（字节）
        progress_callback: Optional[Callable[[float, str], None]] = None  # 进度回调函数
    ) -> str:
        """
        处理文件 - 抽象方法，子类必须实现
        
        Args:
            base_file: 基准文件路径，用于生成新文件的参考文件
            target_size: 目标大小（字节），生成文件的期望大小
            progress_callback: 进度回调函数，接收完成百分比和状态描述文本
            
        Returns:
            生成文件路径，返回生成的文件的完整路径
        """
        pass
    
    def validate_base_file(self, base_file: str) -> None:
        """验证基准文件是否有效 - 检查文件存在性和基本属性"""
        # 检查文件是否存在
        if not os.path.exists(base_file):
            raise BaseFileUnavailableError(f"基准文件不存在: {base_file}")
        
        # 检查是否为文件（不是目录）
        if not os.path.isfile(base_file):
            raise BaseFileUnavailableError(f"基准文件路径不是文件: {base_file}")
        
        # 检查文件大小是否为0
        file_size = get_file_size(base_file)
        if file_size == 0:
            raise BaseFileUnavailableError(f"基准文件大小为0: {base_file}")
        
        # 检查文件扩展名是否与处理器类型匹配
        _, ext = os.path.splitext(base_file)
        if ext.lower()[1:] != self.file_type:
            log_warning(f"文件扩展名({ext})与处理器类型({self.file_type})不匹配")
    
    def verify_result(self, output_file: str, target_size: int) -> None:
        """验证生成的文件是否符合要求 - 检查文件存在和大小误差"""
        # 检查输出文件是否存在
        if not os.path.exists(output_file):
            raise FileGenerationError(f"生成的文件不存在: {output_file}")
        
        # 检查文件大小是否在可接受的误差范围内
        actual_size = get_file_size(output_file)
        is_valid, error_percent = verify_size_error(actual_size, target_size)
        
        if not is_valid:
            log_error(f"生成的文件大小误差过大: {error_percent:.2f}% ({actual_size} vs {target_size})")
            raise FileGenerationError(f"生成的文件大小误差过大: {error_percent:.2f}%")
        
        # 记录成功信息
        log_info(f"文件生成成功: {output_file} (大小: {actual_size}, 误差: {error_percent:.2f}%)")
        
class ProcessorFactory:
    """处理器工厂类 - 用于管理和获取不同类型的文件处理器"""
    
    def __init__(self):
        """初始化工厂 - 创建处理器字典"""
        self.processors: Dict[str, FileProcessor] = {}  # 存储所有注册的处理器，键为文件类型
    
    def register(self, file_type: str, processor: FileProcessor) -> None:
        """注册处理器 - 将处理器实例与文件类型关联"""
        # 统一使用小写的文件类型作为键
        self.processors[file_type.lower()] = processor
        # 记录注册信息
        log_debug(f"已注册处理器: {file_type} -> {processor.__class__.__name__}")
    
    def get_processor(self, file_type: str) -> FileProcessor:
        """获取处理器 - 根据文件类型返回对应的处理器实例"""
        # 查找处理器
        processor = self.processors.get(file_type.lower())
        if not processor:
            # 处理器不存在时抛出异常
            raise ProcessorNotFoundError(f"未找到处理器: {file_type}")
        return processor
        
    def get_supported_types(self) -> list:
        """获取支持的文件类型列表 - 返回所有已注册的文件类型"""
        return list(self.processors.keys())

# 全局工厂实例 - 所有处理器将在此注册
factory = ProcessorFactory()

# 导入必要的模块 - 解决循环导入问题
from file_processors.utils.logger import log_warning
from file_processors.utils.exceptions import ProcessorNotFoundError 