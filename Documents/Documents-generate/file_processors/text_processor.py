import os
import random
import string
from typing import Optional, Callable, List

from file_processors.base import FileProcessor  # 导入处理器基类
from file_processors.utils.helpers import (
    get_file_size, get_output_filepath, get_temp_filepath,
    generate_random_text_blocks, ensure_dir_exists
)  # 辅助函数
from file_processors.utils.logger import log_info, log_debug  # 日志工具
from file_processors.utils.exceptions import InterruptedGenerationError  # 异常类
from config.config import get_config, SizeUnit  # 配置管理

class TextProcessor(FileProcessor):
    """文本文件处理器 - 用于生成指定大小的文本文件"""
    
    file_type = "txt"  # 文件类型标识
    
    def __init__(self):
        super().__init__()
        self.charset = get_config("TXT_CHARSET", "utf-8") # 文本编码，默认UTF-8
        
    def process(
        self, 
        base_file: str, 
        target_size: int, 
        progress_callback: Optional[Callable[[float, str], None]] = None
    ) -> str:
        """
        处理文本文件以达到目标大小
        
        Args:
            base_file: 基础文本文件路径，用作生成内容的基础
            target_size: 目标文件大小（字节），最终生成文件的期望大小
            progress_callback: 进度回调函数，用于更新UI显示进度
            
        Returns:
            生成的文件路径
            
        Raises:
            InterruptedGenerationError: 如果生成过程被中断
        """
        # 校验输入文件有效性
        self.validate_base_file(base_file)
        
        # 确定输出文件路径 - 根据目标大小自动生成文件名
        output_file = get_output_filepath(
            base_file, 
            target_size / 1024 if target_size < 1024*1024 else target_size / (1024*1024), 
            SizeUnit.KB if target_size < 1024*1024 else SizeUnit.MB
        )
        
        # 处理临时文件 - 用于支持断点续传
        temp_file = get_temp_filepath(output_file)
        resumed = False  # 是否从中断处继续
        
        # 检查是否存在可恢复的临时文件
        if os.path.exists(temp_file):
            temp_size = get_file_size(temp_file)
            if temp_size > 0 and temp_size < target_size:
                # 临时文件有效且未完成，可以继续
                log_info(f"发现可恢复的临时文件: {temp_file} ({temp_size} bytes)")
                resumed = True
            else:
                # 临时文件无效或已完成，删除重新开始
                log_debug(f"移除无效的临时文件: {temp_file}")
                os.remove(temp_file)
        
        try:
            # 处理逻辑 - 根据是否需要恢复选择不同的处理路径
            if resumed:
                # 继续生成 - 从临时文件继续添加内容
                bytes_generated = get_file_size(temp_file)
                self._append_to_size(temp_file, target_size, bytes_generated, progress_callback)
            else:
                # 全新生成 - 读取基准文件并创建新文件
                base_content = self._read_base_file(base_file)
                bytes_generated = self._generate_file(base_content, target_size, temp_file, progress_callback)
            
            # 验证结果并移动到最终位置
            ensure_dir_exists(os.path.dirname(output_file))  # 确保输出目录存在
            if os.path.exists(output_file):
                os.remove(output_file)  # 如果输出文件已存在，先删除
            os.rename(temp_file, output_file)  # 将临时文件重命名为最终输出文件
            
            # 验证生成的文件大小是否符合要求
            self.verify_result(output_file, target_size)
            
            return output_file
            
        except Exception as e:
            # 如果失败但有进度，则包装为中断异常以便后续恢复
            if os.path.exists(temp_file) and get_file_size(temp_file) > 0:
                if not isinstance(e, InterruptedGenerationError):
                    bytes_generated = get_file_size(temp_file)
                    # 包装为中断异常，记录已生成的字节数，便于恢复
                    raise InterruptedGenerationError(
                        f"文件生成被中断: {str(e)}",
                        bytes_generated, 
                        target_size,
                        temp_file
                    )
            # 其他异常直接抛出
            raise
            
    def _read_base_file(self, file_path: str) -> str:
        """读取基准文件内容 - 使用指定的字符编码
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件文本内容
        """
        with open(file_path, 'r', encoding=self.charset, errors='ignore') as f:
            return f.read()
    
    def _generate_file(
        self, 
        base_content: str,  # 基准内容
        target_size: int,   # 目标大小
        output_path: str,   # 输出路径
        progress_callback: Optional[Callable[[float, str], None]] = None  # 进度回调
    ) -> int:
        """生成指定大小的文件 - 基于基准内容，通过添加或裁剪达到目标大小
        
        Args:
            base_content: 基准文件内容
            target_size: 目标大小(字节)
            output_path: 输出文件路径
            progress_callback: 进度回调函数
            
        Returns:
            已生成的字节数
        """
        bytes_generated = 0  # 已生成的字节数
        base_bytes = base_content.encode(self.charset)  # 将基准内容转换为字节
        
        with open(output_path, 'wb') as f:
            # 首先写入基准内容
            f.write(base_bytes)
            bytes_generated += len(base_bytes)
            
            # 更新进度
            if progress_callback:
                progress_callback(bytes_generated / target_size, "写入基准内容")
            
            # 如果基准内容已超过目标大小，进行裁剪
            if bytes_generated > target_size:
                f.seek(0)  # 回到文件开头
                f.truncate(target_size)  # 截断到目标大小
                bytes_generated = target_size
                # 更新进度
                if progress_callback:
                    progress_callback(1.0, "裁剪文件完成")
            
            # 如果基准内容不足目标大小，进行填充
            elif bytes_generated < target_size:
                remaining = target_size - bytes_generated  # 剩余需要填充的字节数
                
                # 使用生成器逐块填充，避免一次生成过多内容占用内存
                for text_block in generate_random_text_blocks(remaining):
                    block_bytes = text_block.encode(self.charset)
                    f.write(block_bytes)
                    
                    # 更新已生成字节数和进度
                    bytes_generated += len(block_bytes)
                    if progress_callback:
                        progress_callback(
                            bytes_generated / target_size, 
                            f"填充内容中 ({bytes_generated}/{target_size} bytes)"
                        )
                    
                # 精确调整最终大小
                current_size = f.tell()  # 获取当前文件位置/大小
                if current_size != target_size:
                    if current_size > target_size:
                        # 如果超出目标大小，截断
                        f.seek(0, os.SEEK_END)
                        f.truncate(target_size)
                    else:
                        # 如果不足目标大小，用空字节补充
                        remaining = target_size - current_size
                        f.write(b'\0' * remaining)
                    
                    bytes_generated = target_size
        
        return bytes_generated
    
    def _append_to_size(
        self, 
        file_path: str,   # 文件路径
        target_size: int,  # 目标大小
        current_size: int,  # 当前大小
        progress_callback: Optional[Callable[[float, str], None]] = None  # 进度回调
    ) -> int:
        """向现有文件追加内容直到达到目标大小 - 用于断点续传
        
        Args:
            file_path: 要追加内容的文件路径
            target_size: 目标文件大小
            current_size: 当前文件大小
            progress_callback: 进度回调函数
            
        Returns:
            最终的文件大小
        """
        bytes_generated = current_size  # 当前已生成的字节数
        
        # 更新初始进度
        if progress_callback:
            progress_callback(bytes_generated / target_size, "继续生成文件")
        
        with open(file_path, 'ab') as f:  # 以追加二进制模式打开
            remaining = target_size - bytes_generated  # 剩余需要生成的字节数
            
            # 使用生成器分块填充随机文本
            for text_block in generate_random_text_blocks(remaining):
                block_bytes = text_block.encode(self.charset)
                f.write(block_bytes)
                
                # 更新进度
                bytes_generated += len(block_bytes)
                if progress_callback:
                    progress_callback(
                        bytes_generated / target_size, 
                        f"填充内容中 ({bytes_generated}/{target_size} bytes)"
                    )
            
            # 精确调整最终大小
            current_size = f.tell()
            if current_size != target_size:
                if current_size > target_size:
                    # 如果超出目标大小，截断
                    f.seek(0, os.SEEK_END)
                    f.truncate(target_size)
                else:
                    # 如果不足目标大小，用空字节补充
                    remaining = target_size - current_size
                    f.write(b'\0' * remaining)
                    
            return target_size  # 返回最终大小 