class FileGenerationError(Exception):
    """文件生成过程中的基础异常类 - 所有文件生成异常的基类"""
    pass

class FileTypeError(FileGenerationError):
    """文件类型错误 - 当文件类型不支持或无效时抛出"""
    pass

class FileSizeError(FileGenerationError):
    """文件大小相关错误 - 文件大小不符合要求时抛出"""
    pass

class SizeTooSmallError(FileSizeError):
    """目标大小过小错误 - 当指定的目标大小太小无法处理时抛出"""
    pass

class FormatMismatchError(FileGenerationError):
    """文件格式不匹配错误 - 文件内容与扩展名不符时抛出"""
    pass

class ProcessorNotFoundError(FileGenerationError):
    """处理器未找到错误 - 请求的文件类型没有对应处理器时抛出"""
    pass

class InterruptedGenerationError(FileGenerationError):
    """生成过程被中断错误 - 用于支持断点续传的中断异常
    
    记录中断时的处理进度，便于后续恢复
    """
    
    def __init__(self, message: str, bytes_generated: int, total_bytes: int, temp_file_path: str = None):
        self.bytes_generated = bytes_generated # 已生成的字节数，中断前已处理的数据量
        self.total_bytes = total_bytes # 总字节数，任务的总大小
        self.temp_file_path = temp_file_path # 临时文件路径，用于恢复的文件位置
        # 创建包含详细信息的错误信息
        super().__init__(f"{message} ({bytes_generated}/{total_bytes} bytes)")
        
class BaseFileUnavailableError(FileGenerationError):
    """基准文件不可用错误 - 当基准文件不存在或无法访问时抛出"""
    pass 