import time
from typing import Callable, Optional, Dict, Any
from PySide6.QtCore import QObject, Signal, Slot, QThread  # Qt信号槽机制

from file_processors.utils.logger import log_info, log_error, log_debug  # 日志工具
from file_processors.utils.helpers import calculate_eta, format_bytes  # 辅助函数

class ProgressSignals(QObject):
    """进度信号类 - 定义用于进度通信的Qt信号"""
    progress = Signal(float)  # 进度值信号 (0.0-1.0)
    status = Signal(str)  # 状态文本信号
    completed = Signal(str)  # 完成信号，参数为输出文件路径
    error = Signal(str)  # 错误信号，参数为错误信息
    bytes_processed = Signal(int, int)  # 已处理字节数信号，参数为(已处理字节数,总字节数)

class ProgressManager:
    """进度管理器，不依赖于QT - 用于管理进度状态和计算预计时间"""
    def __init__(self, total_bytes: int, callback: Optional[Callable[[float, str], None]] = None):
        self.total_bytes = total_bytes # 总字节数，任务的总大小
        self.processed_bytes = 0 # 已处理字节数，当前完成的大小
        self.start_time = time.time() # 开始时间，用于计算剩余时间
        self.callback = callback # 回调函数，用于通知进度更新
        self.last_update_time = 0 # 上次更新时间，用于控制更新频率
        self.last_checkpoint = 0 # 上次检查点字节数，用于检测增量
        
    def update(self, bytes_processed: int, status: str = "") -> float:
        """更新进度 - 设置当前已处理的字节数和状态
        
        Args:
            bytes_processed: 已处理的字节数
            status: 当前状态描述
            
        Returns:
            当前进度百分比(0.0-1.0)
        """
        self.processed_bytes = bytes_processed  # 更新已处理字节数
        # 计算进度百分比，确保不超过1.0
        progress = min(1.0, self.processed_bytes / self.total_bytes if self.total_bytes > 0 else 0)
        
        # 如果设置了回调函数，通知进度更新
        if self.callback:
            self.callback(progress, status)
            
        return progress
        
    def increment(self, bytes_increment: int, status: str = "") -> float:
        """增量更新进度 - 添加新处理的字节数
        
        Args:
            bytes_increment: 新增的字节数
            status: 状态描述
            
        Returns:
            更新后的进度百分比
        """
        # 基于当前已处理字节数增加增量
        return self.update(self.processed_bytes + bytes_increment, status)
    
    def get_progress(self) -> float:
        """获取当前进度百分比
        
        Returns:
            进度百分比(0.0-1.0)
        """
        # 计算当前进度，确保不超过1.0
        return min(1.0, self.processed_bytes / self.total_bytes if self.total_bytes > 0 else 0)
    
    def get_eta(self) -> str:
        """获取预计完成时间
        
        Returns:
            预计剩余时间字符串
        """
        # 使用辅助函数计算剩余时间
        return calculate_eta(self.start_time, self.get_progress())
    
    def get_processed_text(self) -> str:
        """获取处理情况文本 - 格式化显示已处理/总大小
        
        Returns:
            格式化的处理进度文本
        """
        # 使用辅助函数格式化字节数
        return f"{format_bytes(self.processed_bytes)}/{format_bytes(self.total_bytes)}"
        
    def should_checkpoint(self, threshold: int = 1024*1024) -> bool:
        """是否应该创建检查点 - 根据增量判断是否需要保存进度
        
        Args:
            threshold: 增量阈值(字节)，默认1MB
            
        Returns:
            如果增量超过阈值，返回True
        """
        # 如果增量超过阈值，建议创建检查点
        return (self.processed_bytes - self.last_checkpoint) >= threshold
    
    def checkpoint(self) -> int:
        """创建检查点并返回增量 - 记录当前进度并计算与上次检查点的差值
        
        Returns:
            自上次检查点以来的字节增量
        """
        # 计算增量
        increment = self.processed_bytes - self.last_checkpoint
        # 更新检查点
        self.last_checkpoint = self.processed_bytes
        return increment

class FileGenerationWorker(QThread):
    """文件生成工作线程 - 在后台执行文件生成任务"""
    def __init__(self, processor, base_file: str, target_size: int, parent=None):
        super().__init__(parent)
        self.processor = processor # 文件处理器实例
        self.base_file = base_file # 基准文件路径
        self.target_size = target_size # 目标文件大小(字节)
        self.signals = ProgressSignals() # Qt信号对象，用于与UI通信
        self._is_cancelled = False # 是否已取消标志
        
    def run(self):
        """线程运行函数 - 执行文件处理任务并发送进度信号"""
        try:
            log_info(f"开始生成文件: {self.base_file} -> {self.target_size} bytes")
            
            # 创建进度回调函数 - 转发进度到信号
            def progress_callback(progress: float, status: str):
                if not self._is_cancelled:  # 只有在未取消时才发送信号
                    self.signals.progress.emit(progress)  # 发送进度信号
                    if status:
                        self.signals.status.emit(status)  # 发送状态信号
            
            # 执行处理器的处理方法
            result_file = self.processor.process(
                self.base_file,
                self.target_size,
                progress_callback
            )
            
            # 如果未取消，发送完成信号
            if not self._is_cancelled:
                log_info(f"文件生成完成: {result_file}")
                self.signals.progress.emit(1.0)  # 设置进度为100%
                self.signals.completed.emit(result_file)  # 发送完成信号
            
        except Exception as e:
            # 处理异常 - 记录日志并发送错误信号
            log_error(f"文件生成失败: {str(e)}")
            if not self._is_cancelled:
                self.signals.error.emit(str(e))  # 发送错误信号
    
    def cancel(self):
        """取消操作 - 设置取消标志"""
        self._is_cancelled = True
        log_debug("文件生成操作已取消") 