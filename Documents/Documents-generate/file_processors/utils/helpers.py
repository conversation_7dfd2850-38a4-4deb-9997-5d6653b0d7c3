import os
import random
import string
import time
from typing import Tuple, Optional, List, Callable, Generator
import re
from config.config import <PERSON>zeUnit, get_config

def convert_size_to_bytes(size: float, unit: SizeUnit) -> int:
    """将指定单位的大小转换为字节数
    
    Args:
        size: 数值大小
        unit: 大小单位(KB/MB)
        
    Returns:
        字节大小(整数)
    """
    if unit == SizeUnit.KB:
        return int(size * 1024)  # KB to bytes
    elif unit == SizeUnit.MB:
        return int(size * 1024 * 1024)  # MB to bytes
    return int(size)  # 默认返回原值

def format_bytes(size_bytes: int) -> str:
    """格式化字节大小为人类可读格式
    
    Args:
        size_bytes: 字节大小
        
    Returns:
        格式化后的大小字符串，如"1.23 MB"
    """
    if size_bytes < 1024:  # 小于1KB
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:  # 小于1MB
        return f"{size_bytes/1024:.2f} KB"
    elif size_bytes < 1024 * 1024 * 1024:  # 小于1GB
        return f"{size_bytes/(1024*1024):.2f} MB"
    return f"{size_bytes/(1024*1024*1024):.2f} GB"  # GB及以上

def get_file_size(file_path: str) -> int:
    """获取文件大小（字节）
    
    Args:
        file_path: 文件路径
        
    Returns:
        文件大小(字节)，如果文件不存在返回0
    """
    return os.path.getsize(file_path) if os.path.exists(file_path) else 0

def ensure_dir_exists(directory: str) -> None:
    """确保目录存在，不存在则创建
    
    Args:
        directory: 目录路径
    """
    if not os.path.exists(directory):
        os.makedirs(directory)

def get_random_text(length: int) -> str:
    """生成指定长度的随机文本
    
    Args:
        length: 字符数量
        
    Returns:
        随机生成的文本
    """
    # 使用字母、数字、标点和空格生成随机文本
    # 空格权重更高(10倍)，使文本更像自然语言
    chars = string.ascii_letters + string.digits + string.punctuation + ' ' * 10
    return ''.join(random.choice(chars) for _ in range(length))

def get_random_paragraph(min_words: int, max_words: int) -> str:
    """生成随机段落，词数在指定范围内
    
    Args:
        min_words: 最小词数
        max_words: 最大词数
        
    Returns:
        随机生成的段落文本，以句号或感叹号结尾
    """
    # 随机确定段落中的词数
    word_count = random.randint(min_words, max_words)
    words = []
    
    # 生成随机单词
    for _ in range(word_count):
        word_len = random.randint(2, 10)  # 随机单词长度
        word = ''.join(random.choice(string.ascii_lowercase) for _ in range(word_len))
        words.append(word)
    
    # 70%概率使用句号，30%概率使用感叹号
    return ' '.join(words) + '。' if random.random() < 0.7 else ' '.join(words) + '！'

def generate_random_text_blocks(target_bytes: int, block_size: int = None) -> Generator[str, None, None]:
    """生成随机文本块的生成器 - 分块产生文本以避免内存溢出
    
    Args:
        target_bytes: 目标总字节数
        block_size: 每个文本块的大小(字节)，默认从配置中获取
        
    Yields:
        文本块，总字节数接近但不超过target_bytes
    """
    # 如果未指定块大小，从配置中获取
    if block_size is None:
        block_size = get_config("TEXT_BLOCK_SIZE", 1024)
    
    bytes_generated = 0
    # 持续生成直到达到目标字节数
    while bytes_generated < target_bytes:
        remaining = target_bytes - bytes_generated
        current_block_size = min(block_size, remaining)  # 调整最后一块的大小
        
        # 生成类似自然文本的内容，带有段落和换行
        text = ""
        while len(text.encode('utf-8')) < current_block_size:
            # 随机段落长度
            para_len = random.randint(
                get_config("DOCX_MIN_WORDS_PER_PARA", 5),
                get_config("DOCX_MAX_WORDS_PER_PARA", 50)
            )
            # 生成段落并添加两个换行符（形成段落间隔）
            para = get_random_paragraph(para_len, para_len + 20) + "\n\n"
            
            # 检查添加此段落是否会超出当前块大小
            if len(text.encode('utf-8')) + len(para.encode('utf-8')) <= current_block_size:
                text += para
            else:
                break
                
        # 确保块大小准确 - 如果超出则截断
        encoded = text.encode('utf-8')
        if len(encoded) > current_block_size:
            # 截断为精确的字节大小，注意处理UTF-8编码边界
            text = encoded[:current_block_size].decode('utf-8', errors='ignore')
        
        # 更新已生成字节数
        encoded_text = text.encode('utf-8')
        bytes_generated += len(encoded_text)
        yield text
        
def calculate_eta(start_time: float, progress: float) -> str:
    """计算预计完成时间
    
    Args:
        start_time: 开始时间戳
        progress: 完成进度(0.0-1.0)
        
    Returns:
        估计剩余时间字符串，格式为"HH:MM:SS"或"MM:SS"
    """
    # 处理边界情况
    if progress <= 0 or progress >= 1:
        return "--:--"  # 无法估计或已完成
    
    # 计算已用时间和预计总时间
    elapsed = time.time() - start_time
    total_time = elapsed / progress  # 预计总时间
    remaining = total_time - elapsed  # 剩余时间
    
    # 转换为时分秒格式
    minutes, seconds = divmod(int(remaining), 60)
    hours, minutes = divmod(minutes, 60)
    
    # 根据时长选择格式
    if hours > 0:
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"  # 包含小时
    else:
        return f"{minutes:02d}:{seconds:02d}"  # 只有分钟和秒

def get_temp_filepath(original_path: str) -> str:
    """获取临时文件路径 - 基于原始文件路径生成临时文件路径
    
    Args:
        original_path: 原始文件路径
        
    Returns:
        临时文件路径
    """
    # 获取临时目录
    temp_dir = get_config("TEMP_DIR", ".temp")
    ensure_dir_exists(temp_dir)  # 确保目录存在
    
    # 提取文件名和扩展名
    filename = os.path.basename(original_path)
    base, ext = os.path.splitext(filename)
    
    # 返回临时文件路径：临时目录 + 文件名_temp + 扩展名
    return os.path.join(temp_dir, f"{base}_temp{ext}")

def get_output_filepath(original_path: str, size: float, unit: SizeUnit) -> str:
    """获取输出文件路径 - 基于原始文件和目标大小生成输出文件路径
    
    Args:
        original_path: 原始文件路径
        size: 目标大小数值
        unit: 大小单位(KB/MB)
        
    Returns:
        输出文件路径，格式为：原文件名_大小单位.扩展名
    """
    # 获取输出目录
    output_dir = get_config("OUTPUT_DIR", "output")
    ensure_dir_exists(output_dir)  # 确保目录存在
    
    # 提取文件名和扩展名
    filename = os.path.basename(original_path)
    base, ext = os.path.splitext(filename)
    
    # 生成大小字符串
    size_str = f"{size}{unit.value}"
    
    # 返回输出文件路径：输出目录 + 文件名_大小单位 + 扩展名
    return os.path.join(output_dir, f"{base}_{size_str}{ext}")

def verify_size_error(actual_size: int, target_size: int) -> Tuple[bool, float]:
    """验证文件大小是否在可接受的误差范围内
    
    Args:
        actual_size: 实际大小(字节)
        target_size: 目标大小(字节)
        
    Returns:
        元组(是否在误差范围内, 误差百分比)
    """
    # 从配置获取可接受的误差百分比
    error_percent = get_config("ACCEPTABLE_ERROR_PERCENTAGE", 5)
    # 计算实际误差百分比
    actual_error = abs(actual_size - target_size) / target_size * 100
    # 返回是否在可接受范围内和实际误差值
    return actual_error <= error_percent, actual_error 