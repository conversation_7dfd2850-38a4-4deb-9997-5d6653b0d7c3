import logging
import os
from datetime import datetime
from config.config import get_config

class Logger:
    """日志管理器类 - 使用单例模式实现的日志系统"""
    _instance = None  # 单例实例
    
    def __new__(cls):
        """实现单例模式 - 确保整个应用只有一个日志器实例"""
        if cls._instance is None:
            # 首次创建实例
            cls._instance = super(Logger, cls).__new__(cls)
            # 初始化日志系统
            cls._instance._setup_logger()
        return cls._instance
    
    def _setup_logger(self):
        """配置日志系统 - 设置日志级别、格式和输出目标"""
        # 从配置获取日志级别
        log_level = getattr(logging, get_config("LOG_LEVEL", "INFO"))
        # 从配置获取日志文件路径
        log_file = get_config("LOG_FILE", "file_generator.log")
        
        # 创建logger对象
        self.logger = logging.getLogger("FileGenerator")
        self.logger.setLevel(log_level)  # 设置日志级别
        
        # 清除已有的处理器，防止重复添加
        if self.logger.handlers:
            self.logger.handlers.clear()
        
        # 创建控制台处理器 - 输出到标准输出
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        
        # 创建文件处理器 - 输出到日志文件
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(log_level)
        
        # 创建格式器 - 定义日志记录的格式
        formatter = logging.Formatter(
            '[%(asctime)s] %(levelname)s [%(name)s.%(funcName)s:%(lineno)d] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        # 设置处理器的格式
        console_handler.setFormatter(formatter)
        file_handler.setFormatter(formatter)
        
        # 添加处理器到logger
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
        
    def get_logger(self):
        """获取日志器实例
        
        Returns:
            配置好的日志器对象
        """
        return self.logger

# 全局日志器实例 - 系统各部分使用这个实例记录日志
logger = Logger().get_logger()

# 便捷日志函数 - 简化日志记录调用
def log_debug(msg): 
    """记录调试级别日志
    
    Args:
        msg: 日志消息
    """
    logger.debug(msg)

def log_info(msg): 
    """记录信息级别日志
    
    Args:
        msg: 日志消息
    """
    logger.info(msg)

def log_warning(msg): 
    """记录警告级别日志
    
    Args:
        msg: 日志消息
    """
    logger.warning(msg)

def log_error(msg): 
    """记录错误级别日志
    
    Args:
        msg: 日志消息
    """
    logger.error(msg)

def log_critical(msg): 
    """记录严重错误级别日志
    
    Args:
        msg: 日志消息
    """
    logger.critical(msg)

def log_exception(msg): 
    """记录异常信息，包括堆栈跟踪
    
    Args:
        msg: 日志消息
    """
    logger.exception(msg) 