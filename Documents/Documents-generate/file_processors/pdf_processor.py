import os
import tempfile
import random
from typing import Optional, Callable, List, Tuple
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet

from file_processors.base import FileProcessor
from file_processors.utils.helpers import (
    get_file_size, get_output_filepath, get_temp_filepath,
    get_random_paragraph, ensure_dir_exists
)
from file_processors.utils.logger import log_info, log_debug, log_error
from file_processors.utils.exceptions import InterruptedGenerationError, FormatMismatchError
from config.config import get_config, SizeUnit

class PdfProcessor(FileProcessor):
    """PDF文件处理器"""
    
    file_type = "pdf"
    
    def __init__(self):
        super().__init__()
        
    def process(
        self, 
        base_file: str, 
        target_size: int, 
        progress_callback: Optional[Callable[[float, str], None]] = None
    ) -> str:
        """
        处理PDF文件以达到目标大小
        
        Args:
            base_file: 基础PDF文件路径
            target_size: 目标文件大小（字节）
            progress_callback: 进度回调函数
            
        Returns:
            生成的文件路径
        """
        # 校验输入文件
        self.validate_base_file(base_file)
        
        # 确定输出文件路径
        output_file = get_output_filepath(
            base_file, 
            target_size / 1024 if target_size < 1024*1024 else target_size / (1024*1024), 
            SizeUnit.KB if target_size < 1024*1024 else SizeUnit.MB
        )
        
        # 处理临时文件
        temp_file = get_temp_filepath(output_file)
        
        try:
            # 先检查基准文件大小
            base_size = get_file_size(base_file)
            
            # 如果基准文件已经超过目标大小，报错
            if base_size > target_size:
                log_error(f"基准文件大小({base_size})已超过目标大小({target_size})")
                raise ValueError("基准文件大小已超过目标大小")
                
            if progress_callback:
                progress_callback(0.1, "分析基准文件")
            
            # 计算需要扩充的页数
            estimated_pages = self._estimate_pages_needed(base_file, base_size, target_size)
            
            # 生成新PDF
            self._generate_pdf_with_size(base_file, temp_file, target_size, estimated_pages, progress_callback)
            
            # 验证结果并移动到最终位置
            ensure_dir_exists(os.path.dirname(output_file))
            if os.path.exists(output_file):
                os.remove(output_file)
            os.rename(temp_file, output_file)
            
            # 验证生成的文件
            self.verify_result(output_file, target_size)
            
            return output_file
            
        except Exception as e:
            # 如果失败但有进度，则包装为中断异常以便后续恢复
            if os.path.exists(temp_file) and get_file_size(temp_file) > 0:
                if not isinstance(e, InterruptedGenerationError):
                    bytes_generated = get_file_size(temp_file)
                    raise InterruptedGenerationError(
                        f"文件生成被中断: {str(e)}",
                        bytes_generated, 
                        target_size,
                        temp_file
                    )
            raise
    
    def _estimate_pages_needed(self, base_file: str, base_size: int, target_size: int) -> int:
        """估算需要的页数"""
        # 尝试获取基准文件的页数
        try:
            import PyPDF2
            with open(base_file, 'rb') as f:
                pdf = PyPDF2.PdfReader(f)
                base_pages = len(pdf.pages)
                
                # 计算每页平均大小
                avg_page_size = base_size / base_pages if base_pages > 0 else 5000
                
                # 估算需要添加的页数
                additional_pages = int((target_size - base_size) / avg_page_size)
                return max(1, additional_pages)
        except:
            # 如果无法分析，则进行粗略估计
            log_debug("无法分析PDF页数，使用估计值")
            # 假设每页大约5KB
            estimated_page_size = 5 * 1024
            additional_pages = int((target_size - base_size) / estimated_page_size)
            return max(10, additional_pages)
    
    def _generate_pdf_with_size(
        self,
        base_file: str,
        output_path: str,
        target_size: int,
        estimated_pages: int,
        progress_callback: Optional[Callable[[float, str], None]] = None
    ) -> None:
        """生成指定大小的PDF文件"""
        # 初始化临时文件
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
            temp_path = temp_file.name
        
        try:
            # 创建新的PDF
            doc = SimpleDocTemplate(
                temp_path,
                pagesize=A4,
                title="Generated PDF",
                author="File Generator"
            )
            
            # 使用样式
            styles = getSampleStyleSheet()
            
            # 创建内容
            story = []
            
            # 生成随机段落
            if progress_callback:
                progress_callback(0.2, "生成PDF内容")
            
            min_words = get_config("DOCX_MIN_WORDS_PER_PARA", 5)
            max_words = get_config("DOCX_MAX_WORDS_PER_PARA", 50)
            
            for i in range(estimated_pages * 4):  # 每页约4个段落
                if i % 20 == 0 and progress_callback:
                    progress_callback(
                        min(0.8, 0.2 + 0.6 * i / (estimated_pages * 4)),
                        f"生成内容中... ({i}/{estimated_pages * 4})"
                    )
                
                para_text = get_random_paragraph(min_words, max_words)
                para = Paragraph(para_text, styles["Normal"])
                story.append(para)
                story.append(Spacer(1, 12))
            
            # 构建文档
            if progress_callback:
                progress_callback(0.8, "构建PDF文档...")
                
            doc.build(story)
            
            # 检查生成的文件大小
            temp_size = get_file_size(temp_path)
            
            # 根据结果决定是否需要额外调整
            if temp_size < target_size:
                # 需要增加大小
                diff = target_size - temp_size
                log_debug(f"PDF生成后大小不足，需要增加 {diff} 字节")
                
                # 添加到PDF中的隐藏数据
                self._append_hidden_data(temp_path, diff)
                
            elif temp_size > target_size:
                # 需要减小大小
                log_debug(f"PDF生成后大小过大，需要优化")
                # 简单方案：压缩PDF或重新生成更少页数
                
            # 将临时文件移动到输出路径
            ensure_dir_exists(os.path.dirname(output_path))
            if os.path.exists(output_path):
                os.remove(output_path)
            os.rename(temp_path, output_path)
            
            # 标记完成
            if progress_callback:
                progress_callback(1.0, "PDF生成完成")
                
        finally:
            # 清理临时文件
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except:
                    pass
    
    def _append_hidden_data(self, pdf_path: str, size_to_add: int) -> None:
        """向PDF文件附加隐藏数据"""
        # 简单方案：在PDF文件末尾添加注释块
        with open(pdf_path, 'ab') as f:
            # PDF注释格式：% 后面的内容在渲染时被忽略
            comment_prefix = b"\n%FileGeneratorPadding: "
            remaining = size_to_add - len(comment_prefix)
            
            if remaining > 0:
                f.write(comment_prefix)
                # 写入随机数据作为填充
                chunk_size = min(remaining, 8192)
                bytes_written = 0
                
                while bytes_written < remaining:
                    current_chunk_size = min(chunk_size, remaining - bytes_written)
                    random_bytes = bytes([random.randint(32, 126) for _ in range(current_chunk_size)])
                    f.write(random_bytes)
                    bytes_written += current_chunk_size 