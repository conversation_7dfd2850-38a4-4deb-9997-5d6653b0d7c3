"""文件处理器模块 - 管理不同类型文件的处理器实现"""

# 导入工厂实例 - 用于注册和管理处理器
from file_processors.base import factory

# 导入各种类型的文件处理器类
from file_processors.text_processor import TextProcessor  # 文本文件处理器
from file_processors.docx_processor import DocxProcessor  # Word文档处理器
from file_processors.pdf_processor import PdfProcessor    # PDF文件处理器
from file_processors.image_processor import PngProcessor  # PNG图像处理器
from file_processors.video_processor import Mp4Processor  # MP4视频处理器

# 注册所有处理器到工厂 - 将文件类型与处理器实例关联
factory.register("txt", TextProcessor())   # 注册txt文本处理器
factory.register("docx", DocxProcessor())  # 注册docx文档处理器
factory.register("pdf", PdfProcessor())    # 注册pdf文件处理器
factory.register("png", PngProcessor())    # 注册png图像处理器
factory.register("mp4", Mp4Processor())    # 注册mp4视频处理器

# 便捷函数 - 简化外部调用
def get_processor(file_type):
    """获取处理器实例 - 根据文件类型返回对应的处理器
    
    Args:
        file_type: 文件类型字符串，如'txt', 'docx'等
        
    Returns:
        对应类型的处理器实例
        
    Raises:
        ProcessorNotFoundError: 如果找不到对应类型的处理器
    """
    return factory.get_processor(file_type)

def get_supported_types():
    """获取支持的文件类型列表 - 返回所有已注册的文件类型
    
    Returns:
        文件类型字符串列表，如['txt', 'docx', 'pdf', 'png', 'mp4']
    """
    return factory.get_supported_types()
