import os
import random
import tempfile
import shutil
from typing import Optional, Callable, List, Tuple
from docx import Document

from file_processors.base import FileProcessor
from file_processors.utils.helpers import (
    get_file_size, get_output_filepath, get_temp_filepath,
    get_random_paragraph, ensure_dir_exists
)
from file_processors.utils.logger import log_info, log_debug, log_error
from file_processors.utils.exceptions import InterruptedGenerationError, FormatMismatchError
from config.config import get_config, SizeUnit

class DocxProcessor(FileProcessor):
    """DOCX文件处理器"""
    
    file_type = "docx"
    
    def __init__(self):
        super().__init__()
        self.min_words = get_config("DOCX_MIN_WORDS_PER_PARA", 5) # 段落最小词数
        self.max_words = get_config("DOCX_MAX_WORDS_PER_PARA", 50) # 段落最大词数
        
    def process(
        self, 
        base_file: str, 
        target_size: int, 
        progress_callback: Optional[Callable[[float, str], None]] = None
    ) -> str:
        """
        处理DOCX文件以达到目标大小
        
        Args:
            base_file: 基础DOCX文件路径
            target_size: 目标文件大小（字节）
            progress_callback: 进度回调函数
            
        Returns:
            生成的文件路径
        """
        # 校验输入文件
        self.validate_base_file(base_file)
        
        # 确定输出文件路径
        output_file = get_output_filepath(
            base_file, 
            target_size / 1024 if target_size < 1024*1024 else target_size / (1024*1024), 
            SizeUnit.KB if target_size < 1024*1024 else SizeUnit.MB
        )
        
        # 处理临时文件
        temp_file = get_temp_filepath(output_file)
        
        try:
            # 读取基准文档
            try:
                doc = Document(base_file)
            except Exception as e:
                raise FormatMismatchError(f"无法读取DOCX文件: {str(e)}")
                
            # 先拷贝基准文件作为起点
            ensure_dir_exists(os.path.dirname(temp_file))
            shutil.copy2(base_file, temp_file)
            
            if progress_callback:
                progress_callback(0.1, "已复制基准文件")
                
            # 增量添加内容直到达到目标大小
            self._extend_to_size(doc, temp_file, target_size, progress_callback)
            
            # 验证结果并移动到最终位置
            ensure_dir_exists(os.path.dirname(output_file))
            if os.path.exists(output_file):
                os.remove(output_file)
            os.rename(temp_file, output_file)
            
            # 验证生成的文件
            self.verify_result(output_file, target_size)
            
            return output_file
        
        except Exception as e:
            # 如果失败但有进度，则包装为中断异常以便后续恢复
            if os.path.exists(temp_file) and get_file_size(temp_file) > 0:
                if not isinstance(e, InterruptedGenerationError):
                    bytes_generated = get_file_size(temp_file)
                    raise InterruptedGenerationError(
                        f"文件生成被中断: {str(e)}",
                        bytes_generated, 
                        target_size,
                        temp_file
                    )
            raise
    
    def _extend_to_size(
        self,
        doc: Document,
        output_path: str,
        target_size: int,
        progress_callback: Optional[Callable[[float, str], None]] = None
    ) -> None:
        """
        扩展DOCX文件至目标大小
        """
        # 初始大小
        current_size = get_file_size(output_path)
        
        # 设置初始增长参数
        batch_size = 5  # 每批次添加段落数
        paragraphs_added = 0
        growth_factor = 1.2  # 每批次大小增长因子
        
        # 在文档结尾逐步添加段落直到达到目标大小
        while current_size < target_size:
            # 计算进度
            progress = min(0.95, current_size / target_size)
            if progress_callback:
                progress_callback(progress, f"添加内容中... {paragraphs_added}段落已添加")
            
            # 加载临时文件到内存
            doc = Document(output_path)
            
            # 添加一批段落
            for _ in range(batch_size):
                para_text = get_random_paragraph(self.min_words, self.max_words)
                doc.add_paragraph(para_text)
                paragraphs_added += 1
            
            # 保存并获取新大小
            doc.save(output_path)
            new_size = get_file_size(output_path)
            
            # 如果没有增长，增加批次大小
            if new_size == current_size:
                batch_size = int(batch_size * growth_factor)
                log_debug(f"文件大小未增长，增加批次大小至{batch_size}")
            else:
                # 估算剩余迭代次数
                size_per_batch = new_size - current_size
                remaining_size = target_size - new_size
                if size_per_batch > 0:
                    estimated_batches = remaining_size / size_per_batch
                    # 如果估计批次较多，则增加批次大小
                    if estimated_batches > 20:
                        batch_size = int(batch_size * growth_factor)
            
            current_size = new_size
            
        # 精确调整大小（通过添加或删除内容）
        if current_size != target_size:
            self._fine_tune_size(output_path, target_size, progress_callback)
                
    def _fine_tune_size(
        self,
        file_path: str,
        target_size: int,
        progress_callback: Optional[Callable[[float, str], None]] = None
    ) -> None:
        """精确调整DOCX文件的大小以匹配目标大小"""
        current_size = get_file_size(file_path)
        
        if current_size == target_size:
            return
            
        if progress_callback:
            progress_callback(0.95, "精确调整大小...")
            
        # 根据需要增加或减少
        if current_size > target_size:
            # 创建临时文件夹处理DOCX
            with tempfile.TemporaryDirectory() as temp_dir:
                # 解压DOCX
                from zipfile import ZipFile
                extract_path = os.path.join(temp_dir, "extracted")
                os.makedirs(extract_path, exist_ok=True)
                
                with ZipFile(file_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_path)
                
                # 修改文档属性以减小大小
                document_xml = os.path.join(extract_path, "word", "document.xml")
                if os.path.exists(document_xml):
                    # 读取并修改XML内容（这里简化处理）
                    with open(document_xml, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查当前文件大小和目标大小之间的差值
                    diff = current_size - target_size
                    
                    # 根据差值截断内容（简单方法）
                    if diff > 0 and len(content) > diff:
                        # 从尾部裁剪，保证XML仍然有效
                        # 实际应用中可能需要更复杂的XML解析
                        idx = content.rfind('</w:p>')
                        if idx > 0:
                            new_content = content[:idx] + '</w:p></w:body></w:document>'
                            with open(document_xml, 'w', encoding='utf-8') as f:
                                f.write(new_content)
                
                # 重新打包DOCX
                from zipfile import ZipFile, ZIP_DEFLATED
                temp_output = os.path.join(temp_dir, "output.docx")
                
                with ZipFile(temp_output, 'w', ZIP_DEFLATED) as zipf:
                    for root, _, files in os.walk(extract_path):
                        for file in files:
                            file_path_in_zip = os.path.relpath(os.path.join(root, file), extract_path)
                            zipf.write(os.path.join(root, file), file_path_in_zip)
                
                # 替换原始文件
                shutil.copy2(temp_output, file_path)
        else:
            # 需要增加大小
            doc = Document(file_path)
            
            # 添加隐藏内容来增加大小
            remaining = target_size - current_size
            hidden_text = 'X' * remaining  # 简单填充
            
            # 添加一个隐藏段落
            p = doc.add_paragraph()
            run = p.add_run(hidden_text[:min(len(hidden_text), 1000)])  # 避免过大
            font = run.font
            font.hidden = True  # 设置为隐藏文本
            
            doc.save(file_path)
        
        # 最终检查
        if progress_callback:
            progress_callback(1.0, "大小调整完成")
        
        # 如果调整后仍有显著误差，记录警告
        adjusted_size = get_file_size(file_path)
        if abs(adjusted_size - target_size) > target_size * 0.05:
            log_warning(f"DOCX精确调整后仍有误差：{adjusted_size} vs {target_size}")

# 从文件处理器日志模块导入
from file_processors.utils.logger import log_warning 