import os
import tempfile
import subprocess
import random
import json
from typing import Optional, Callable, Dict, List, Tuple

from file_processors.base import FileProcessor
from file_processors.utils.helpers import (
    get_file_size, get_output_filepath, get_temp_filepath,
    ensure_dir_exists
)
from file_processors.utils.logger import log_info, log_debug, log_error
from file_processors.utils.exceptions import InterruptedGenerationError, FormatMismatchError
from config.config import get_config, SizeUnit

class Mp4Processor(FileProcessor):
    """MP4视频处理器"""
    
    file_type = "mp4"
    
    def __init__(self):
        super().__init__()
        
    def process(
        self, 
        base_file: str, 
        target_size: int, 
        progress_callback: Optional[Callable[[float, str], None]] = None
    ) -> str:
        """
        处理MP4视频以达到目标大小
        
        Args:
            base_file: 基础MP4文件路径
            target_size: 目标文件大小（字节）
            progress_callback: 进度回调函数
            
        Returns:
            生成的文件路径
        """
        # 校验输入文件
        self.validate_base_file(base_file)
        
        # 检查ffmpeg是否可用
        try:
            subprocess.run(['ffmpeg', '-version'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
        except (subprocess.SubprocessError, FileNotFoundError):
            raise RuntimeError("需要安装ffmpeg才能处理视频文件")
        
        # 确定输出文件路径
        output_file = get_output_filepath(
            base_file, 
            target_size / 1024 if target_size < 1024*1024 else target_size / (1024*1024), 
            SizeUnit.KB if target_size < 1024*1024 else SizeUnit.MB
        )
        
        # 处理临时文件
        temp_file = get_temp_filepath(output_file)
        
        try:
            # 先检查基准文件大小
            base_size = get_file_size(base_file)
            
            if progress_callback:
                progress_callback(0.1, "分析视频文件")
                
            # 获取视频信息
            video_info = self._get_video_info(base_file)
            
            # 决定处理策略
            if base_size > target_size:
                # 基准文件大于目标大小，需要减小
                if progress_callback:
                    progress_callback(0.2, "压缩视频以达到目标大小")
                
                # 计算目标比特率
                return self._compress_video(base_file, temp_file, target_size, video_info, progress_callback)
            else:
                # 基准文件小于目标大小，需要增加
                if progress_callback:
                    progress_callback(0.2, "扩充视频以达到目标大小")
                
                return self._expand_video(base_file, temp_file, target_size, video_info, progress_callback)
                
        except Exception as e:
            # 如果失败但有进度，则包装为中断异常以便后续恢复
            if os.path.exists(temp_file) and get_file_size(temp_file) > 0:
                if not isinstance(e, InterruptedGenerationError):
                    bytes_generated = get_file_size(temp_file)
                    raise InterruptedGenerationError(
                        f"文件生成被中断: {str(e)}",
                        bytes_generated, 
                        target_size,
                        temp_file
                    )
            raise
    
    def _get_video_info(self, video_path: str) -> Dict:
        """获取视频文件信息"""
        cmd = [
            'ffprobe', 
            '-v', 'quiet', 
            '-print_format', 'json', 
            '-show_format', 
            '-show_streams', 
            video_path
        ]
        
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        if result.returncode != 0:
            log_error(f"获取视频信息失败: {result.stderr.decode('utf-8', errors='ignore')}")
            raise FormatMismatchError(f"无法解析视频文件: {video_path}")
            
        info = json.loads(result.stdout)
        return info
    
    def _get_video_duration(self, video_info: Dict) -> float:
        """获取视频时长（秒）"""
        try:
            return float(video_info['format']['duration'])
        except (KeyError, ValueError):
            return 0.0
    
    def _get_video_bitrate(self, video_info: Dict) -> int:
        """获取视频比特率（bit/s）"""
        try:
            return int(video_info['format']['bit_rate'])
        except (KeyError, ValueError):
            return 0
    
    def _compress_video(
        self, 
        input_path: str, 
        output_path: str, 
        target_size: int,
        video_info: Dict,
        progress_callback: Optional[Callable[[float, str], None]] = None
    ) -> str:
        """压缩视频到目标大小"""
        # 获取视频时长
        duration = self._get_video_duration(video_info)
        if duration <= 0:
            raise ValueError("无法获取视频时长")
            
        # 计算目标比特率
        # 目标大小(bytes) * 8(bits/byte) / 时长(s) = 比特率(bits/s)
        target_bitrate = int((target_size * 8) / duration * 0.95)  # 留5%余量
        
        if progress_callback:
            progress_callback(0.3, f"计算目标比特率: {target_bitrate/1000:.2f}kbps")
            
        # 创建临时目录
        ensure_dir_exists(os.path.dirname(output_path))
            
        # 执行压缩
        cmd = [
            'ffmpeg',
            '-i', input_path,
            '-b:v', f'{target_bitrate}',
            '-maxrate', f'{target_bitrate * 1.5}',
            '-bufsize', f'{target_bitrate * 2}',
            '-c:v', 'libx264',
            '-pass', '1',
            '-f', 'null',
            '/dev/null'
        ]
        
        if progress_callback:
            progress_callback(0.4, "两次编码压缩 (第一次)")
            
        # 执行第一次编码（只分析）
        subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
        
        # 第二次编码
        cmd = [
            'ffmpeg',
            '-i', input_path,
            '-b:v', f'{target_bitrate}',
            '-maxrate', f'{target_bitrate * 1.5}',
            '-bufsize', f'{target_bitrate * 2}',
            '-c:v', 'libx264',
            '-pass', '2',
            '-c:a', 'aac',
            '-b:a', '128k',
            output_path
        ]
        
        if progress_callback:
            progress_callback(0.6, "两次编码压缩 (第二次)")
            
        # 执行第二次编码
        subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
        
        # 检查生成的文件大小
        result_size = get_file_size(output_path)
        
        # 如果大小仍超过目标，使用CRF方法再次压缩
        if result_size > target_size:
            if progress_callback:
                progress_callback(0.8, "微调视频质量...")
            
            # 使用CRF方法再次压缩
            # CRF从23开始，值越大质量越低文件越小
            crf = 23
            max_attempts = 5
            
            while result_size > target_size and max_attempts > 0:
                crf += 2
                tmp_output = f"{output_path}.tmp"
                
                cmd = [
                    'ffmpeg',
                    '-i', input_path,
                    '-c:v', 'libx264',
                    '-crf', str(crf),
                    '-c:a', 'aac',
                    '-b:a', '128k',
                    tmp_output
                ]
                
                subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
                
                if os.path.exists(tmp_output):
                    os.replace(tmp_output, output_path)
                    result_size = get_file_size(output_path)
                    max_attempts -= 1
        
        # 验证结果并移动到最终位置
        final_output = get_output_filepath(
            input_path, 
            target_size / 1024 if target_size < 1024*1024 else target_size / (1024*1024), 
            SizeUnit.KB if target_size < 1024*1024 else SizeUnit.MB
        )
        
        ensure_dir_exists(os.path.dirname(final_output))
        if os.path.exists(final_output):
            os.remove(final_output)
        os.rename(output_path, final_output)
        
        # 验证生成的文件
        self.verify_result(final_output, target_size)
        
        if progress_callback:
            progress_callback(1.0, "视频压缩完成")
            
        return final_output
    
    def _expand_video(
        self, 
        input_path: str, 
        output_path: str, 
        target_size: int,
        video_info: Dict,
        progress_callback: Optional[Callable[[float, str], None]] = None
    ) -> str:
        """扩展视频到目标大小"""
        base_size = get_file_size(input_path)
        
        if progress_callback:
            progress_callback(0.3, "计算目标比特率")
            
        # 方法1：增加比特率
        # 获取视频时长
        duration = self._get_video_duration(video_info)
        if duration <= 0:
            raise ValueError("无法获取视频时长")
            
        # 计算目标比特率
        target_bitrate = int((target_size * 8) / duration * 0.95)  # 留5%余量
        current_bitrate = self._get_video_bitrate(video_info)
        
        # 比特率增加至少需要50%才有效
        if target_bitrate > current_bitrate * 1.5:
            if progress_callback:
                progress_callback(0.4, f"增加视频比特率: {current_bitrate/1000:.2f}kbps -> {target_bitrate/1000:.2f}kbps")
                
            # 创建临时目录
            ensure_dir_exists(os.path.dirname(output_path))
                
            # 执行转码
            cmd = [
                'ffmpeg',
                '-i', input_path,
                '-b:v', f'{target_bitrate}',
                '-maxrate', f'{target_bitrate * 1.5}',
                '-bufsize', f'{target_bitrate * 2}',
                '-c:v', 'libx264',
                '-c:a', 'aac',
                '-b:a', '192k',
                output_path
            ]
            
            subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
            
            # 检查生成的文件大小
            result_size = get_file_size(output_path)
            
            if result_size >= target_size * 0.95 and result_size <= target_size * 1.05:
                # 验证结果并移动到最终位置
                final_output = get_output_filepath(
                    input_path, 
                    target_size / 1024 if target_size < 1024*1024 else target_size / (1024*1024), 
                    SizeUnit.KB if target_size < 1024*1024 else SizeUnit.MB
                )
                
                ensure_dir_exists(os.path.dirname(final_output))
                if os.path.exists(final_output):
                    os.remove(final_output)
                os.rename(output_path, final_output)
                
                # 验证生成的文件
                self.verify_result(final_output, target_size)
                
                if progress_callback:
                    progress_callback(1.0, "视频处理完成")
                    
                return final_output
        
        # 方法2：如果增加比特率不足以达到目标，则附加数据
        if progress_callback:
            progress_callback(0.7, "添加附加数据...")
            
        # 先复制原始文件
        ensure_dir_exists(os.path.dirname(output_path))
        with open(input_path, 'rb') as src, open(output_path, 'wb') as dst:
            dst.write(src.read())
            
        # 在文件末尾添加自定义数据块
        current_size = get_file_size(output_path)
        remaining = target_size - current_size
        
        if remaining > 0:
            with open(output_path, 'ab') as f:
                # 添加特殊标记，确保不影响视频播放
                f.write(b'\0\0\0\0FILEGENERATOR_PADDING')
                remaining -= 24  # 减去标记长度
                
                # 分块写入填充数据
                chunk_size = 1024 * 1024  # 1MB
                while remaining > 0:
                    write_size = min(chunk_size, remaining)
                    # 随机填充数据
                    f.write(os.urandom(write_size))
                    remaining -= write_size
                    
                    if progress_callback:
                        current = get_file_size(output_path)
                        progress = 0.7 + 0.2 * current / target_size
                        progress_callback(min(0.9, progress), f"填充数据: {current/1024/1024:.2f}MB/{target_size/1024/1024:.2f}MB")
        
        # 验证结果并移动到最终位置
        final_output = get_output_filepath(
            input_path, 
            target_size / 1024 if target_size < 1024*1024 else target_size / (1024*1024), 
            SizeUnit.KB if target_size < 1024*1024 else SizeUnit.MB
        )
        
        ensure_dir_exists(os.path.dirname(final_output))
        if os.path.exists(final_output):
            os.remove(final_output)
        os.rename(output_path, final_output)
        
        # 验证生成的文件
        self.verify_result(final_output, target_size)
        
        if progress_callback:
            progress_callback(1.0, "视频处理完成")
            
        return final_output 