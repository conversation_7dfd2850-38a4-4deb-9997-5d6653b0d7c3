from enum import Enum
from typing import Dict, Any

class FileType(Enum):
    """文件类型枚举 - 定义支持的文件类型"""
    TXT = "txt"   # 文本文件
    DOCX = "docx"  # Word文档
    PDF = "pdf"   # PDF文档
    PNG = "png"   # PNG图像
    MP4 = "mp4"   # MP4视频

class SizeUnit(Enum):
    """文件大小单位枚举 - 定义支持的文件大小单位"""
    KB = "KB"  # 千字节
    MB = "MB"  # 兆字节

# 全局配置字典 - 集中管理所有可配置参数
CONFIG: Dict[str, Any] = {
    # 应用设置
    "APP_NAME": "文件生成工具",        # 应用名称，显示在窗口标题
    "APP_VERSION": "1.0.0",           # 应用版本号
    
    # 文件处理设置
    "ACCEPTABLE_ERROR_PERCENTAGE": 5,  # 允许的大小误差百分比，最终文件大小可能有±5%的误差
    "TEMP_DIR": ".temp",               # 临时文件目录，存放生成过程中的临时文件
    "OUTPUT_DIR": "output",            # 输出文件目录，存放最终生成的文件
    
    # 填充设置
    "TEXT_BLOCK_SIZE": 1024,           # 文本填充块大小(字节)，生成文本时每块的大小
    "BINARY_BLOCK_SIZE": 4096,         # 二进制填充块大小(字节)，生成二进制数据时每块的大小
    
    # 处理器设置
    "TXT_CHARSET": "utf-8",            # 文本文件字符集，文本文件使用的编码
    "DOCX_MIN_WORDS_PER_PARA": 5,      # docx段落最小字数，生成Word文档时段落的最小单词数
    "DOCX_MAX_WORDS_PER_PARA": 50,     # docx段落最大字数，生成Word文档时段落的最大单词数
    
    # 线程设置
    "THREAD_UPDATE_INTERVAL": 100,     # 线程更新UI间隔(ms)，后台线程向UI发送更新的频率
    
    # 日志设置
    "LOG_LEVEL": "INFO",               # 日志级别，可选DEBUG, INFO, WARNING, ERROR, CRITICAL
    "LOG_FILE": "file_generator.log",  # 日志文件路径
}

# 便捷访问配置的函数
def get_config(key: str, default: Any = None) -> Any:
    """获取配置项值 - 根据键获取配置，如果不存在则返回默认值
    
    Args:
        key: 配置项的键
        default: 如果键不存在时返回的默认值
        
    Returns:
        配置项的值或默认值
    """
    return CONFIG.get(key, default) 